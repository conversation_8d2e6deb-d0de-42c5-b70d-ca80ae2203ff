# SendMe Logistics API Documentation

## Overview

This is a comprehensive Node.js + Express backend API for the SendMe Logistics platform, providing complete CRUD operations and model-specific functionality for all 8 MongoDB models.

## Base URL
```
http://localhost:5000/api
```

## Authentication

All protected routes require a JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Models Overview

The API provides endpoints for 8 main models:

1. **User** - User management with role-based access (customer/driver/admin)
2. **Booking** - Core booking system with tracking and status management
3. **Vehicle** - Vehicle types with pricing and availability management
4. **PromoCode** - Sophisticated discount system with usage tracking
5. **Review** - Rating and review system for drivers and customers
6. **Notification** - Multi-channel notification system
7. **CmsContent** - Content management with versioning
8. **SupportTicket** - Comprehensive support ticket system

## API Endpoints

### Authentication Routes (`/api/auth`)
- `POST /register` - Register new user
- `POST /login` - User login
- `POST /otp-request` - Request OTP for verification
- `POST /otp-verify` - Verify OTP
- `POST /refresh-token` - Refresh JWT token
- `POST /logout` - User logout
- `GET /me` - Get current user profile

### User Management (`/api/users`)

#### Standard CRUD Operations
- `GET /api/users` - Get all users (Admin only)
- `GET /api/users/:id` - Get user by ID (Admin or own profile)
- `POST /api/users` - Create new user (Admin only)
- `PUT /api/users/:id` - Update user (Admin or own profile)
- `PATCH /api/users/:id` - Partial update user
- `DELETE /api/users/:id` - Delete user (Admin only)

#### User-Specific Operations
- `PATCH /api/users/:id/status` - Update user status (Admin only)
- `PATCH /api/users/:id/location` - Update driver location (Driver only)
- `PATCH /api/users/:id/online-status` - Toggle driver online status (Driver only)
- `GET /api/users/drivers/nearby` - Get nearby drivers

### Booking Management (`/api/bookings`)

#### Standard CRUD Operations
- `GET /api/bookings` - Get all bookings with filtering
- `GET /api/bookings/:id` - Get booking by ID
- `POST /api/bookings` - Create new booking
- `PUT /api/bookings/:id` - Update booking
- `PATCH /api/bookings/:id` - Partial update booking
- `DELETE /api/bookings/:id` - Cancel booking

#### Booking-Specific Operations
- `PATCH /api/bookings/:id/status` - Update booking status
- `PATCH /api/bookings/:id/assign-driver` - Assign driver (Admin only)
- `PATCH /api/bookings/:id/location` - Update tracking location (Driver only)
- `GET /api/bookings/:id/tracking` - Get tracking information
- `POST /api/bookings/:id/messages` - Add message to booking

### Vehicle Management (`/api/vehicles`)

#### Standard CRUD Operations
- `GET /api/vehicles` - Get all vehicle types (Public)
- `GET /api/vehicles/:id` - Get vehicle type by ID (Public)
- `POST /api/vehicles` - Create vehicle type (Admin only)
- `PUT /api/vehicles/:id` - Update vehicle type (Admin only)
- `PATCH /api/vehicles/:id` - Partial update vehicle type (Admin only)
- `DELETE /api/vehicles/:id` - Delete vehicle type (Admin only)

#### Vehicle-Specific Operations
- `PATCH /api/vehicles/:id/availability` - Update availability (Admin only)
- `PATCH /api/vehicles/:id/pricing` - Update pricing (Admin only)

### PromoCode Management (`/api/promocodes`)

#### Standard CRUD Operations
- `GET /api/promocodes` - Get all promo codes (Admin only)
- `GET /api/promocodes/:id` - Get promo code by ID (Admin only)
- `POST /api/promocodes` - Create promo code (Admin only)
- `PUT /api/promocodes/:id` - Update promo code (Admin only)
- `PATCH /api/promocodes/:id` - Partial update promo code (Admin only)
- `DELETE /api/promocodes/:id` - Delete promo code (Admin only)

#### PromoCode-Specific Operations
- `POST /api/promocodes/validate` - Validate promo code for user
- `GET /api/promocodes/applicable` - Get applicable promo codes for user

### Review Management (`/api/reviews`)

#### Standard CRUD Operations
- `GET /api/reviews` - Get all reviews with filtering
- `GET /api/reviews/:id` - Get review by ID
- `POST /api/reviews` - Create new review
- `PUT /api/reviews/:id` - Update review
- `PATCH /api/reviews/:id` - Partial update review
- `DELETE /api/reviews/:id` - Delete review (Admin only)

#### Review-Specific Operations
- `PATCH /api/reviews/:id/moderate` - Moderate review (Admin only)
- `POST /api/reviews/:id/vote` - Vote on review helpfulness
- `POST /api/reviews/:id/response` - Add response to review (Reviewee only)
- `GET /api/reviews/user/:userId/summary` - Get user rating summary (Public)

### Notification Management (`/api/notifications`)

#### Standard CRUD Operations
- `GET /api/notifications` - Get all notifications
- `GET /api/notifications/:id` - Get notification by ID
- `POST /api/notifications` - Create notification (Admin only)
- `PUT /api/notifications/:id` - Update notification (Admin only)
- `PATCH /api/notifications/:id` - Partial update notification (Admin only)
- `DELETE /api/notifications/:id` - Delete notification (Admin only)

#### Notification-Specific Operations
- `PATCH /api/notifications/:id/read` - Mark notification as read
- `PATCH /api/notifications/mark-all-read` - Mark all notifications as read
- `GET /api/notifications/stats` - Get notification statistics

### CMS Content Management (`/api/cms`)

#### Standard CRUD Operations
- `GET /api/cms` - Get all CMS content
- `GET /api/cms/:identifier` - Get content by ID or slug
- `POST /api/cms` - Create CMS content (Admin only)
- `PUT /api/cms/:id` - Update CMS content (Admin only)
- `PATCH /api/cms/:id` - Partial update CMS content (Admin only)
- `DELETE /api/cms/:id` - Delete CMS content (Admin only)

#### CMS-Specific Operations
- `PATCH /api/cms/:id/publish` - Publish/unpublish content (Admin only)

### Support Ticket Management (`/api/support`)

#### Standard CRUD Operations
- `GET /api/support` - Get all support tickets
- `GET /api/support/:id` - Get support ticket by ID
- `POST /api/support` - Create support ticket
- `PUT /api/support/:id` - Update support ticket
- `PATCH /api/support/:id` - Partial update support ticket
- `DELETE /api/support/:id` - Delete support ticket (Admin only)

#### Support-Specific Operations
- `PATCH /api/support/:id/assign` - Assign ticket to agent (Admin only)
- `PATCH /api/support/:id/escalate` - Escalate ticket (Admin only)
- `PATCH /api/support/:id/resolve` - Resolve ticket (Admin only)
- `PATCH /api/support/:id/reopen` - Reopen ticket
- `POST /api/support/:id/messages` - Add message to ticket
- `GET /api/support/agent/:agentId` - Get tickets for agent
- `GET /api/support/stats` - Get support statistics (Admin only)

## Query Parameters

### Pagination
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)

### Sorting
- `sortBy` - Field to sort by
- `sortOrder` - Sort direction (`asc` or `desc`)

### Filtering
- `status` - Filter by status
- `search` - Search in relevant fields
- Various model-specific filters

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "errors": [
    // Validation errors if applicable
  ]
}
```

### Paginated Response
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

## Authentication & Authorization

### Roles
- **Customer** - Can create bookings, reviews, support tickets
- **Driver** - Can update location, accept bookings, create reviews
- **Admin** - Full access to all endpoints

### Protected Routes
Most routes require authentication. Public routes include:
- Vehicle listings
- CMS content (published only)
- User rating summaries
- Health check and API documentation

## Installation & Setup

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables in `.env`

3. Start the server:
```bash
npm run dev
```

## Features

- **Comprehensive CRUD Operations** for all 8 models
- **Role-based Authorization** with proper access controls
- **Input Validation** using express-validator
- **Error Handling** with detailed error responses
- **Pagination, Filtering, and Sorting** for all list endpoints
- **Model-specific Operations** tailored to business logic
- **Proper HTTP Status Codes** and RESTful conventions
- **Detailed Logging** for debugging and monitoring

## Testing

Use the provided Postman collection to test all endpoints. The collection includes:
- Authentication flows
- CRUD operations for all models
- Model-specific functionality
- Error scenarios
- Different user roles

## Support

For API support or questions, create a support ticket through the `/api/support` endpoint or contact the development team.
