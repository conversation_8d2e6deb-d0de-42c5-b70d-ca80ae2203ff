{"name": "sendme-logistics-backend", "version": "1.0.0", "description": "Multi-role logistics and delivery booking platform backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node src/seeders/index.js seed", "seed:clear": "node src/seeders/index.js clear", "seed:dev": "node src/seeders/index.js dev", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["logistics", "delivery", "booking", "express", "mongodb", "nodejs"], "author": "SendMe Logistics", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.9.2", "helmet": "^7.0.0", "cors": "^2.8.5", "express-rate-limit": "^6.10.0", "dotenv": "^16.3.1", "winston": "^3.10.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "cloudinary": "^1.40.0", "stripe": "^13.5.0", "twilio": "^4.15.0", "nodemailer": "^6.9.4", "socket.io": "^4.7.2", "firebase-admin": "^11.10.1", "axios": "^1.5.0", "moment": "^2.29.4", "redis": "^4.6.8", "express-validator": "^7.0.1", "express-async-handler": "^1.2.0", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.14", "compression": "^1.7.4", "express-slow-down": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "eslint": "^8.47.0"}, "engines": {"node": ">=18.0.0"}}